{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/community.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  Avatar,\n  AvatarFallback,\n  AvatarImage,\n} from '@repo/design-system/components/ui/avatar';\nimport { Button } from '@repo/design-system/components/ui/button';\nimport type { Dictionary } from '@repo/internationalization';\nimport { ExternalLink, Github, MessageCircle } from 'lucide-react';\nimport { useEffect, useState } from 'react';\n\ntype CommunityProps = {\n  dictionary: Dictionary;\n};\n\n// Sample community reviews data\nconst communityReviews = [\n  {\n    id: 1,\n    username: 'justingodev',\n    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n    content: 'What @cubentbase + @nextjs is amazing! 🔥 Really excited into a proof-of-concept and already have a lot of the functionality in place 🤯 🤯 🤯',\n    platform: 'twitter'\n  },\n  {\n    id: 2,\n    username: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',\n    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n    content: 'Using @cubentbase I\\'m really impressed with the developers and see it in general. Despite being a bit dubious about the fact that I have to say I really don\\'t miss anything. The whole experience feels very robust and secure.',\n    platform: 'twitter'\n  },\n  {\n    id: 3,\n    username: 'joerell',\n    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',\n    content: 'This weekend I made a personal record 🏆 on the last time spent creating an application with social login / permissions, database, cdn, and for free. Thanks to @cubentbase and @vercel.',\n    platform: 'twitter'\n  },\n  {\n    id: 4,\n    username: 'stevemarshall',\n    avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',\n    content: 'Working on my own cubent project. I want this to be my first because I\\'m not a backend engineer and I\\'m getting it done.',\n    platform: 'twitter'\n  },\n  {\n    id: 5,\n    username: 'BraydenCoyer',\n    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',\n    content: 'New to @cubentbase I was really impressed with the developers and see it in general. Despite being a bit dubious about the fact that I have to say I really don\\'t miss anything.',\n    platform: 'twitter'\n  },\n  {\n    id: 6,\n    username: 'axaxone',\n    avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?w=150&h=150&fit=crop&crop=face',\n    content: 'Completed @cubentbase is it. It had the best experience I\\'ve had with a database. Despite being a bit dubious about the fact that I have to say I really don\\'t miss anything.',\n    platform: 'twitter'\n  },\n  {\n    id: 7,\n    username: 'GenericCassel',\n    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n    content: 'Biggest @cubentbase is amazing! Really impressed with the developers and see it in general.',\n    platform: 'twitter'\n  },\n  {\n    id: 8,\n    username: 'alexdeveloper',\n    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face',\n    content: 'Just shipped my first app using @cubentbase and the developer experience is incredible! The AI features saved me hours of debugging.',\n    platform: 'twitter'\n  },\n  {\n    id: 9,\n    username: 'sarahcodes',\n    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n    content: 'The context intelligence in @cubentbase is a game changer. It understands my codebase better than I do sometimes! 😅',\n    platform: 'twitter'\n  },\n  {\n    id: 10,\n    username: 'mikefullstack',\n    avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',\n    content: 'Been using @cubentbase for 3 months now. The AI screenshot analysis feature is mind-blowing - it catches UI issues I completely miss.',\n    platform: 'twitter'\n  },\n  {\n    id: 11,\n    username: 'devjenna',\n    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n    content: 'Finally found a coding assistant that actually understands context! @cubentbase has become essential to my workflow.',\n    platform: 'twitter'\n  },\n  {\n    id: 12,\n    username: 'codemaster_tom',\n    avatar: 'https://images.unsplash.com/photo-1566492031773-4f4e44671d66?w=150&h=150&fit=crop&crop=face',\n    content: 'The multi-language support in @cubentbase is fantastic. Seamlessly switching between JS, Python, and Rust in the same project.',\n    platform: 'twitter'\n  },\n  {\n    id: 13,\n    username: 'reactdev_anna',\n    avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',\n    content: 'The autonomous agent mode in @cubentbase is absolutely mind-blowing. I gave it a complex task and it just... did it. No back and forth, just results.',\n    platform: 'twitter'\n  },\n  {\n    id: 14,\n    username: 'fullstack_dev',\n    avatar: 'https://images.unsplash.com/photo-1527980965255-d3b416303d12?w=150&h=150&fit=crop&crop=face',\n    content: 'Switched from Copilot to @cubentbase and the difference is night and day. The context awareness is incredible - it actually understands my project structure.',\n    platform: 'twitter'\n  },\n  {\n    id: 15,\n    username: 'techsavvy_lisa',\n    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',\n    content: 'The natural language coding in @cubentbase is revolutionary. I can literally describe what I want and it builds it perfectly. This is the future of development.',\n    platform: 'twitter'\n  },\n  {\n    id: 16,\n    username: 'backend_guru',\n    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',\n    content: 'Documentation generation with @cubentbase saved our team weeks of work. It understands the code context and writes better docs than we ever could manually.',\n    platform: 'twitter'\n  },\n  {\n    id: 17,\n    username: 'startup_founder',\n    avatar: 'https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150&h=150&fit=crop&crop=face',\n    content: 'The file operations in @cubentbase are so smart. It reads my entire workspace, understands the architecture, and makes perfect modifications across multiple files.',\n    platform: 'twitter'\n  },\n  {\n    id: 18,\n    username: 'ai_enthusiast',\n    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',\n    content: 'Terminal integration in @cubentbase is a game changer. It can run commands, analyze output, and fix issues automatically. It\\'s like having a DevOps expert on call.',\n    platform: 'twitter'\n  },\n  {\n    id: 19,\n    username: 'solo_developer',\n    avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',\n    content: 'As a solo founder, @cubentbase is my secret weapon. It handles complex coding tasks while I focus on business strategy. Productivity through the roof! 🚀',\n    platform: 'twitter'\n  },\n  {\n    id: 20,\n    username: 'security_expert',\n    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',\n    content: 'The custom modes in @cubentbase are brilliant. I created a security auditor mode and it found vulnerabilities I completely missed. This tool is incredible.',\n    platform: 'twitter'\n  },\n  {\n    id: 21,\n    username: 'webdev_pro',\n    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',\n    content: 'Browser automation with @cubentbase is pure magic. It can interact with web pages, test functionality, and even debug UI issues. Mind = blown 🤯',\n    platform: 'twitter'\n  },\n  {\n    id: 22,\n    username: 'mobile_dev',\n    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',\n    content: 'The code generation quality in @cubentbase is unmatched. It writes cleaner code than most junior developers and follows best practices automatically.',\n    platform: 'twitter'\n  },\n  {\n    id: 23,\n    username: 'data_scientist',\n    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=150&h=150&fit=crop&crop=face',\n    content: 'Using @cubentbase for data analysis scripts has been incredible. It understands pandas, numpy, and matplotlib better than I do sometimes! 📊',\n    platform: 'twitter'\n  },\n  {\n    id: 24,\n    username: 'devops_ninja',\n    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',\n    content: 'The Docker and Kubernetes integration in @cubentbase is seamless. It can write deployment configs, debug container issues, and optimize resource usage.',\n    platform: 'twitter'\n  }\n];\n\nexport const Community = ({ dictionary }: CommunityProps) => {\n  const [currentIndex, setCurrentIndex] = useState(0);\n\n  // Auto-scroll animation\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentIndex((prevIndex) => (prevIndex + 1) % communityReviews.length);\n    }, 2000); // Faster animation\n\n    return () => clearInterval(interval);\n  }, []);\n\n  // Create animated columns for the animation effect\n  const createAnimatedColumn = (startIndex: number, direction: 'left' | 'right') => {\n    const reviews = [...communityReviews, ...communityReviews]; // Duplicate for seamless loop\n    const animationClass = direction === 'left' ? 'animate-scroll-left' : 'animate-scroll-right';\n\n    return (\n      <div className={`flex gap-8 ${animationClass}`} style={{ animationDelay: `${startIndex * 0.5}s` }}>\n        {reviews.map((review, index) => (\n          <div\n            key={`${review.id}-${index}`}\n            className=\"flex-shrink-0 w-80 bg-neutral-700/50 backdrop-blur-sm rounded-lg p-6 border border-neutral-600/40 shadow-lg hover:bg-neutral-600/60 transition-colors duration-300\"\n          >\n            <div className=\"flex items-start gap-3\">\n              <Avatar className=\"h-10 w-10 flex-shrink-0\">\n                <AvatarImage src={review.avatar} alt={review.username} />\n                <AvatarFallback>{review.username.slice(0, 2).toUpperCase()}</AvatarFallback>\n              </Avatar>\n              <div className=\"flex-1 min-w-0\">\n                <div className=\"flex items-center gap-2 mb-2\">\n                  <span className=\"text-white font-medium text-sm\">@{review.username}</span>\n                  {/* X (Twitter) Logo with shadow */}\n                  <div className=\"relative\">\n                    <svg\n                      viewBox=\"0 0 24 24\"\n                      className=\"w-4 h-4 fill-white drop-shadow-lg\"\n                      style={{\n                        filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.3)) drop-shadow(0 0 8px rgba(255,255,255,0.1))'\n                      }}\n                    >\n                      <path d=\"M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z\"/>\n                    </svg>\n                  </div>\n                </div>\n                <p className=\"text-neutral-300 text-sm leading-relaxed\">\n                  {review.content}\n                </p>\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"w-full py-20 lg:py-40 bg-[#0a0a0a] relative overflow-hidden\">\n      {/* Grid background */}\n      <div\n        className=\"absolute inset-0 opacity-20\"\n        style={{\n          backgroundImage: `\n            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\n          `,\n          backgroundSize: '40px 40px'\n        }}\n      />\n      {/* Background gradient effects */}\n      <div className=\"absolute inset-0 bg-gradient-to-b from-transparent via-white/11 to-transparent\" />\n      \n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10\">\n        <div className=\"flex flex-col items-center gap-10\">\n          {/* Header */}\n          <div className=\"text-center\">\n            <h2 className=\"text-white font-regular text-3xl tracking-tighter md:text-5xl mb-4\">\n              Join the community\n            </h2>\n            <p className=\"text-neutral-400 text-lg max-w-2xl mx-auto\">\n              Discover what our community has to say about their Cubent experience.\n            </p>\n          </div>\n\n          {/* Community Buttons */}\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <Button\n              variant=\"outline\"\n              className=\"bg-neutral-900/50 border-neutral-700 text-white hover:bg-neutral-800/50 backdrop-blur-sm\"\n              asChild\n            >\n              <a\n                href=\"https://github.com/cubent/discussions\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"flex items-center gap-2\"\n              >\n                <Github className=\"w-4 h-4\" />\n                GitHub discussions\n                <ExternalLink className=\"w-3 h-3\" />\n              </a>\n            </Button>\n            <Button\n              variant=\"outline\"\n              className=\"bg-neutral-900/50 border-neutral-700 text-white hover:bg-neutral-800/50 backdrop-blur-sm\"\n              asChild\n            >\n              <a\n                href=\"https://discord.gg/cubent\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"flex items-center gap-2\"\n              >\n                <MessageCircle className=\"w-4 h-4\" />\n                Discord\n                <ExternalLink className=\"w-3 h-3\" />\n              </a>\n            </Button>\n          </div>\n\n          {/* Animated Reviews */}\n          <div className=\"w-full max-w-7xl mx-auto\">\n            <div className=\"space-y-12 mask-gradient\">\n              {/* First column - scrolling left */}\n              <div className=\"overflow-hidden\">\n                {createAnimatedColumn(0, 'left')}\n              </div>\n\n              {/* Second column - scrolling right */}\n              <div className=\"overflow-hidden\">\n                {createAnimatedColumn(1, 'right')}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAKA;AAEA;AAAA;AAAA;AACA;;;AAVA;;;;;AAgBA,gCAAgC;AAChC,MAAM,mBAAmB;IACvB;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,UAAU;IACZ;CACD;AAEM,MAAM,YAAY,CAAC,EAAE,UAAU,EAAkB;;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,wBAAwB;IACxB,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,WAAW;gDAAY;oBAC3B;wDAAgB,CAAC,YAAc,CAAC,YAAY,CAAC,IAAI,iBAAiB,MAAM;;gBAC1E;+CAAG,OAAO,mBAAmB;YAE7B;uCAAO,IAAM,cAAc;;QAC7B;8BAAG,EAAE;IAEL,mDAAmD;IACnD,MAAM,uBAAuB,CAAC,YAAoB;QAChD,MAAM,UAAU;eAAI;eAAqB;SAAiB,EAAE,8BAA8B;QAC1F,MAAM,iBAAiB,cAAc,SAAS,wBAAwB;QAEtE,qBACE,4SAAC;YAAI,WAAW,CAAC,WAAW,EAAE,gBAAgB;YAAE,OAAO;gBAAE,gBAAgB,GAAG,aAAa,IAAI,CAAC,CAAC;YAAC;sBAC7F,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,4SAAC;oBAEC,WAAU;8BAEV,cAAA,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,8JAAA,CAAA,SAAM;gCAAC,WAAU;;kDAChB,4SAAC,8JAAA,CAAA,cAAW;wCAAC,KAAK,OAAO,MAAM;wCAAE,KAAK,OAAO,QAAQ;;;;;;kDACrD,4SAAC,8JAAA,CAAA,iBAAc;kDAAE,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,WAAW;;;;;;;;;;;;0CAE1D,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAK,WAAU;;oDAAiC;oDAAE,OAAO,QAAQ;;;;;;;0DAElE,4SAAC;gDAAI,WAAU;0DACb,cAAA,4SAAC;oDACC,SAAQ;oDACR,WAAU;oDACV,OAAO;wDACL,QAAQ;oDACV;8DAEA,cAAA,4SAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;kDAId,4SAAC;wCAAE,WAAU;kDACV,OAAO,OAAO;;;;;;;;;;;;;;;;;;mBAzBhB,GAAG,OAAO,EAAE,CAAC,CAAC,EAAE,OAAO;;;;;;;;;;IAiCtC;IAEA,qBACE,4SAAC;QAAI,WAAU;;0BAEb,4SAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC;;;UAGlB,CAAC;oBACD,gBAAgB;gBAClB;;;;;;0BAGF,4SAAC;gBAAI,WAAU;;;;;;0BAEf,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;sCAEb,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAG,WAAU;8CAAqE;;;;;;8CAGnF,4SAAC;oCAAE,WAAU;8CAA6C;;;;;;;;;;;;sCAM5D,4SAAC;4BAAI,WAAU;;8CACb,4SAAC,8JAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,OAAO;8CAEP,cAAA,4SAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;;0DAEV,4SAAC,6RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;0DAE9B,4SAAC,6SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG5B,4SAAC,8JAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,OAAO;8CAEP,cAAA,4SAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;;0DAEV,4SAAC,+SAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;4CAAY;0DAErC,4SAAC,6SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAM9B,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;;kDAEb,4SAAC;wCAAI,WAAU;kDACZ,qBAAqB,GAAG;;;;;;kDAI3B,4SAAC;wCAAI,WAAU;kDACZ,qBAAqB,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzC;GA1Ia;KAAA", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/accordion.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\n\nfunction Accordion({\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Root>) {\n  return <AccordionPrimitive.Root data-slot=\"accordion\" {...props} />\n}\n\nfunction AccordionItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Item>) {\n  return (\n    <AccordionPrimitive.Item\n      data-slot=\"accordion-item\"\n      className={cn(\"border-b last:border-b-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AccordionTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Trigger>) {\n  return (\n    <AccordionPrimitive.Header className=\"flex\">\n      <AccordionPrimitive.Trigger\n        data-slot=\"accordion-trigger\"\n        className={cn(\n          \"focus-visible:border-ring focus-visible:ring-ring/50 flex flex-1 items-start justify-between gap-4 rounded-md py-4 text-left text-sm font-medium transition-all outline-none hover:underline focus-visible:ring-[3px] disabled:pointer-events-none disabled:opacity-50 [&[data-state=open]>svg]:rotate-180\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <ChevronDownIcon className=\"text-muted-foreground pointer-events-none size-4 shrink-0 translate-y-0.5 transition-transform duration-200\" />\n      </AccordionPrimitive.Trigger>\n    </AccordionPrimitive.Header>\n  )\n}\n\nfunction AccordionContent({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof AccordionPrimitive.Content>) {\n  return (\n    <AccordionPrimitive.Content\n      data-slot=\"accordion-content\"\n      className=\"data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down overflow-hidden text-sm\"\n      {...props}\n    >\n      <div className={cn(\"pt-0 pb-4\", className)}>{children}</div>\n    </AccordionPrimitive.Content>\n  )\n}\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,UAAU,EACjB,GAAG,OACkD;IACrD,qBAAO,4SAAC,+QAAA,CAAA,OAAuB;QAAC,aAAU;QAAa,GAAG,KAAK;;;;;;AACjE;KAJS;AAMT,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,4SAAC,+QAAA,CAAA,OAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,4SAAC,+QAAA,CAAA,SAAyB;QAAC,WAAU;kBACnC,cAAA,4SAAC,+QAAA,CAAA,UAA0B;YACzB,aAAU;YACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,8SACA;YAED,GAAG,KAAK;;gBAER;8BACD,4SAAC,+SAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;MApBS;AAsBT,SAAS,iBAAiB,EACxB,SAAS,EACT,QAAQ,EACR,GAAG,OACqD;IACxD,qBACE,4SAAC,+QAAA,CAAA,UAA0B;QACzB,aAAU;QACV,WAAU;QACT,GAAG,KAAK;kBAET,cAAA,4SAAC;YAAI,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,aAAa;sBAAa;;;;;;;;;;;AAGnD;MAdS", "debugId": null}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/animated-title.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\n\nconst words = ['generate', 'debug', 'explain', 'optimize'];\n\nexport const AnimatedTitle = () => {\n  const [currentWordIndex, setCurrentWordIndex] = useState(0);\n  const [isVisible, setIsVisible] = useState(true);\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setIsVisible(false);\n      \n      setTimeout(() => {\n        setCurrentWordIndex((prev) => (prev + 1) % words.length);\n        setIsVisible(true);\n      }, 300); // Half of the transition duration\n    }, 3500); // Change word every 3.5 seconds\n\n    return () => clearInterval(interval);\n  }, []);\n\n  return (\n    <h1 className=\"max-w-4xl text-center font-regular text-5xl tracking-tighter md:text-7xl relative z-10\">\n      AI that can{' '}\n      <span\n        className={`inline-block transition-all duration-500 ease-in-out ${\n          isVisible\n            ? 'opacity-100 transform translate-y-0'\n            : 'opacity-0 transform -translate-y-2'\n        }`}\n        style={{\n          background: 'linear-gradient(135deg, #8b5a8c 0%, #6366f1 50%, #3b82f6 100%)',\n          WebkitBackgroundClip: 'text',\n          WebkitTextFillColor: 'transparent',\n          backgroundClip: 'text',\n          minWidth: '200px',\n          display: 'inline-block',\n          textAlign: 'left'\n        }}\n      >\n        {words[currentWordIndex]},\n      </span>{' '}\n      so you can ship smarter.\n    </h1>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIA,MAAM,QAAQ;IAAC;IAAY;IAAS;IAAW;CAAW;AAEnD,MAAM,gBAAgB;;IAC3B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,WAAW;oDAAY;oBAC3B,aAAa;oBAEb;4DAAW;4BACT;oEAAoB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,MAAM,MAAM;;4BACvD,aAAa;wBACf;2DAAG,MAAM,kCAAkC;gBAC7C;mDAAG,OAAO,gCAAgC;YAE1C;2CAAO,IAAM,cAAc;;QAC7B;kCAAG,EAAE;IAEL,qBACE,4SAAC;QAAG,WAAU;;YAAyF;YACzF;0BACZ,4SAAC;gBACC,WAAW,CAAC,qDAAqD,EAC/D,YACI,wCACA,sCACJ;gBACF,OAAO;oBACL,YAAY;oBACZ,sBAAsB;oBACtB,qBAAqB;oBACrB,gBAAgB;oBAChB,UAAU;oBACV,SAAS;oBACT,WAAW;gBACb;;oBAEC,KAAK,CAAC,iBAAiB;oBAAC;;;;;;;YACnB;YAAI;;;;;;;AAIlB;GAzCa;KAAA", "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/mockup.tsx"], "sourcesContent": ["'use client';\n\nimport Image from 'next/image';\nimport Link from 'next/link';\nimport { MoveRight, X } from 'lucide-react';\nimport { useState } from 'react';\n\n// Feature data\nconst features = [\n  {\n    id: 1,\n    title: \"Context-aware code intelligence system\",\n    image: \"/images/cubent-feature-1.png\",\n    alt: \"Context Intelligence\",\n    description: \"<PERSON><PERSON><PERSON> understands your entire codebase context, making intelligent suggestions that align with your project's architecture and patterns.\",\n    content: [\n      \"At the heart of modern software development lies the challenge of maintaining context across vast codebases. <PERSON><PERSON><PERSON> revolutionizes this experience by providing deep, contextual understanding of your entire project ecosystem. Our advanced AI doesn't just read your code—it comprehends the intricate relationships between components, understands your architectural decisions, and learns from your coding patterns.\",\n\n      \"Unlike traditional code assistants that work in isolation, <PERSON><PERSON><PERSON> maintains a comprehensive map of your project's structure, dependencies, and design patterns. This enables it to make suggestions that aren't just syntactically correct, but architecturally sound and consistent with your existing codebase. Whether you're working on a microservices architecture, a monolithic application, or a complex distributed system, <PERSON><PERSON><PERSON> adapts to your specific context.\",\n\n      \"The intelligence extends beyond simple code completion. <PERSON><PERSON><PERSON> analyzes cross-file dependencies, understands the impact of changes across your entire system, and can predict potential issues before they arise. This proactive approach to development helps teams maintain code quality while accelerating their development velocity, making it an indispensable tool for serious product development teams.\"\n    ]\n  },\n  {\n    id: 2,\n    title: \"Screenshot to code in seconds plus\",\n    image: \"/images/cubent-feature-2.png\",\n    alt: \"AI Screenshot Analysis\",\n    description: \"Transform screenshots and designs into working code instantly. Cubent analyzes visual elements and generates pixel-perfect implementations.\",\n    content: [\n      \"In today's fast-paced development environment, the ability to rapidly prototype and implement designs is crucial for staying competitive. Cubent's revolutionary screenshot-to-code technology bridges the gap between design and implementation, allowing developers to transform visual mockups into functional code in seconds rather than hours.\",\n\n      \"Our advanced computer vision algorithms analyze every pixel of your designs, understanding not just what elements are present, but how they should behave and interact. The system recognizes common UI patterns, understands responsive design principles, and generates code that follows modern best practices. Whether you're working with Figma designs, hand-drawn sketches, or competitor screenshots, Cubent can interpret and implement them accurately.\",\n\n      \"The generated code isn't just a static representation—it's production-ready, accessible, and optimized for performance. Cubent automatically handles responsive breakpoints, generates semantic HTML, applies appropriate ARIA labels, and ensures cross-browser compatibility. This means you can go from concept to working prototype in minutes, allowing for rapid iteration and faster time-to-market for your products.\"\n    ]\n  },\n  {\n    id: 3,\n    title: \"Deep codebase understanding engine\",\n    image: \"/images/cubent-feature-3.png\",\n    alt: \"Smart Code Editing\",\n    description: \"Experience intelligent code editing that understands your intent. Cubent provides contextual suggestions and automated improvements.\",\n    content: [\n      \"Perfect code isn't just about functionality—it's about maintainability, performance, and elegance. Cubent's intelligent editing capabilities go far beyond traditional autocomplete, offering a sophisticated understanding of code quality, performance implications, and best practices. Every suggestion is crafted with the goal of not just making your code work, but making it exceptional.\",\n\n      \"The system continuously analyzes your code for potential improvements, from micro-optimizations that enhance performance to architectural suggestions that improve maintainability. Cubent understands the nuances of different programming languages, frameworks, and design patterns, allowing it to provide highly specific and relevant recommendations tailored to your technology stack and coding style.\",\n\n      \"What sets Cubent apart is its ability to learn and adapt to your team's specific standards and preferences. It recognizes your coding conventions, understands your project's unique requirements, and evolves its suggestions to match your team's definition of perfect code. This results in a more consistent codebase, reduced technical debt, and a development experience that feels truly personalized and intelligent.\"\n    ]\n  }\n];\n\nexport const Mockup = () => {\n  const [selectedFeature, setSelectedFeature] = useState<typeof features[0] | null>(null);\n\n  return (\n  <div className=\"w-full relative\">\n    {/* Grid background for GIF section */}\n    <div\n      className=\"absolute inset-0 opacity-20\"\n      style={{\n        backgroundImage: `\n          linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\n          linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\n        `,\n        backgroundSize: '40px 40px'\n      }}\n    />\n    <div className=\"container mx-auto px-2 sm:px-4 lg:px-6 relative z-10\">\n      <div className=\"flex flex-col items-center justify-center gap-2 py-4\">\n        <div className=\"relative w-full max-w-7xl\">\n          <div className=\"relative overflow-hidden\">\n            <Image\n              src=\"/images/Cubent.Dev.gif\"\n              alt=\"Cubent Editor Interface - Code editing with AI assistance\"\n              width={1200}\n              height={800}\n              className=\"w-full h-auto object-cover rounded-lg\"\n              priority\n              unoptimized\n            />\n            {/* Soft glow effect */}\n            <div className=\"absolute -inset-4 bg-gradient-to-r from-blue-500/20 via-purple-500/20 to-pink-500/20 rounded-2xl blur-xl opacity-30 -z-10\" />\n          </div>\n        </div>\n      </div>\n    </div>\n\n    {/* Made for modern product teams section */}\n    <div className=\"relative container mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32\">\n      {/* Grid background */}\n      <div\n        className=\"absolute inset-0 opacity-20\"\n        style={{\n          backgroundImage: `\n            linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\n            linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\n          `,\n          backgroundSize: '40px 40px'\n        }}\n      />\n      {/* Top section - Title on left, description on right */}\n      <div className=\"relative z-10 flex flex-col lg:flex-row gap-8 lg:gap-16 items-start mb-16 lg:mb-20 max-w-5xl mx-auto\">\n        {/* Left side - Title */}\n        <div className=\"flex-1 max-w-md\">\n          <h2 className=\"text-4xl lg:text-5xl font-regular tracking-tighter text-white\">\n            AI-powered development that understands your code\n          </h2>\n        </div>\n\n        {/* Right side - Description and link */}\n        <div className=\"flex-1 max-w-lg\">\n          <p className=\"text-lg text-muted-foreground leading-relaxed mb-4\">\n            Cubent transforms how developers work by providing intelligent, context-aware assistance that learns from your codebase. From instant screenshot-to-code conversion to deep architectural understanding, we're building the future of software development.\n          </p>\n          <Link href=\"#\" className=\"text-white hover:text-muted-foreground transition-colors inline-flex items-center gap-2\">\n            Make the switch <MoveRight className=\"h-4 w-4\" />\n          </Link>\n        </div>\n      </div>\n\n      {/* Bottom section - Three feature cards in a row */}\n      <div className=\"relative z-10 max-w-6xl mx-auto\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {features.map((feature, index) => (\n            <div\n              key={feature.id}\n              onClick={() => setSelectedFeature(feature)}\n              className={`group relative bg-[#1a1a1a] rounded-3xl overflow-hidden hover:bg-[#222222] transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-black/50 cursor-pointer ${\n                index === 2 ? 'md:col-span-2 lg:col-span-1' : ''\n              }`}\n            >\n              <div className=\"relative aspect-square w-full overflow-hidden\">\n                <Image\n                  src={feature.image}\n                  alt={feature.alt}\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n              <div className=\"p-6 pr-16\">\n                <h3 className=\"text-xl font-medium text-white mb-2 leading-tight break-words\">\n                  {feature.title}\n                </h3>\n              </div>\n              <div className=\"absolute bottom-5 right-5 w-12 h-12 border border-white/20 rounded-full flex items-center justify-center text-white/70 group-hover:bg-white/10 group-hover:text-white transition-all duration-300\">\n                <span className=\"text-2xl leading-none\">+</span>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </div>\n\n    {/* Modal */}\n    {selectedFeature && (\n      <div className=\"fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4\">\n        <div className=\"bg-[#1a1a1a] rounded-3xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n          {/* Modal Header */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setSelectedFeature(null)}\n              className=\"absolute top-6 right-6 z-10 w-10 h-10 bg-black/20 hover:bg-black/40 rounded-full flex items-center justify-center text-white/70 hover:text-white transition-all duration-200\"\n            >\n              <X size={20} />\n            </button>\n\n            {/* Feature Image */}\n            <div className=\"relative h-80 w-full overflow-hidden\">\n              <Image\n                src={selectedFeature.image}\n                alt={selectedFeature.alt}\n                fill\n                className=\"object-cover\"\n              />\n              <div className=\"absolute inset-0 bg-gradient-to-t from-[#1a1a1a] via-transparent to-transparent\" />\n            </div>\n          </div>\n\n          {/* Modal Content */}\n          <div className=\"p-8 max-h-[50vh] overflow-y-auto\">\n            <h2 className=\"text-3xl font-bold text-white mb-6\">\n              {selectedFeature.title}\n            </h2>\n\n            <p className=\"text-gray-300 text-lg mb-8 leading-relaxed\">\n              {selectedFeature.description}\n            </p>\n\n            <div className=\"space-y-6\">\n              {selectedFeature.content.map((paragraph, index) => (\n                <p key={index} className=\"text-gray-300 leading-relaxed text-base\">\n                  {paragraph}\n                </p>\n              ))}\n            </div>\n\n            <div className=\"mt-8 pt-6 border-t border-white/10\">\n              <p className=\"text-gray-400 text-sm leading-relaxed\">\n                Experience the power of AI-driven development with Cubent's advanced features designed to accelerate your workflow and improve code quality. Join thousands of developers who have already transformed their development process with intelligent, context-aware coding assistance.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    )}\n  </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAOA,eAAe;AACf,MAAM,WAAW;IACf;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,KAAK;QACL,aAAa;QACb,SAAS;YACP;YAEA;YAEA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,KAAK;QACL,aAAa;QACb,SAAS;YACP;YAEA;YAEA;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,OAAO;QACP,KAAK;QACL,aAAa;QACb,SAAS;YACP;YAEA;YAEA;SACD;IACH;CACD;AAEM,MAAM,SAAS;;IACpB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAA6B;IAElF,qBACA,4SAAC;QAAI,WAAU;;0BAEb,4SAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC;;;QAGlB,CAAC;oBACD,gBAAgB;gBAClB;;;;;;0BAEF,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;;8CACb,4SAAC,+OAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;oCACV,QAAQ;oCACR,WAAW;;;;;;8CAGb,4SAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOvB,4SAAC;gBAAI,WAAU;;kCAEb,4SAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,CAAC;;;UAGlB,CAAC;4BACD,gBAAgB;wBAClB;;;;;;kCAGF,4SAAC;wBAAI,WAAU;;0CAEb,4SAAC;gCAAI,WAAU;0CACb,cAAA,4SAAC;oCAAG,WAAU;8CAAgE;;;;;;;;;;;0CAMhF,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAE,WAAU;kDAAqD;;;;;;kDAGlE,4SAAC,8QAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;4CAA0F;0DACjG,4SAAC,uSAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAM3C,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,4SAAC;oCAEC,SAAS,IAAM,mBAAmB;oCAClC,WAAW,CAAC,kLAAkL,EAC5L,UAAU,IAAI,gCAAgC,IAC9C;;sDAEF,4SAAC;4CAAI,WAAU;sDACb,cAAA,4SAAC,+OAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,KAAK;gDAClB,KAAK,QAAQ,GAAG;gDAChB,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,4SAAC;4CAAI,WAAU;sDACb,cAAA,4SAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;;;;;;sDAGlB,4SAAC;4CAAI,WAAU;sDACb,cAAA,4SAAC;gDAAK,WAAU;0DAAwB;;;;;;;;;;;;mCApBrC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;YA6BxB,iCACC,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;sCAEb,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CAEV,cAAA,4SAAC,mRAAA,CAAA,IAAC;wCAAC,MAAM;;;;;;;;;;;8CAIX,4SAAC;oCAAI,WAAU;;sDACb,4SAAC,+OAAA,CAAA,UAAK;4CACJ,KAAK,gBAAgB,KAAK;4CAC1B,KAAK,gBAAgB,GAAG;4CACxB,IAAI;4CACJ,WAAU;;;;;;sDAEZ,4SAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAKnB,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAG,WAAU;8CACX,gBAAgB,KAAK;;;;;;8CAGxB,4SAAC;oCAAE,WAAU;8CACV,gBAAgB,WAAW;;;;;;8CAG9B,4SAAC;oCAAI,WAAU;8CACZ,gBAAgB,OAAO,CAAC,GAAG,CAAC,CAAC,WAAW,sBACvC,4SAAC;4CAAc,WAAU;sDACtB;2CADK;;;;;;;;;;8CAMZ,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC;wCAAE,WAAU;kDAAwC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnE;GA5Ja;KAAA", "debugId": null}}, {"offset": {"line": 1138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/packages/design-system/components/ui/carousel.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport useEmblaCarousel, {\n  type UseEmblaCarouselType,\n} from \"embla-carousel-react\"\nimport { ArrowLeft, ArrowRight } from \"lucide-react\"\n\nimport { cn } from \"@repo/design-system/lib/utils\"\nimport { But<PERSON> } from \"@repo/design-system/components/ui/button\"\n\ntype CarouselApi = UseEmblaCarouselType[1]\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>\ntype CarouselOptions = UseCarouselParameters[0]\ntype CarouselPlugin = UseCarouselParameters[1]\n\ntype CarouselProps = {\n  opts?: CarouselOptions\n  plugins?: CarouselPlugin\n  orientation?: \"horizontal\" | \"vertical\"\n  setApi?: (api: CarouselApi) => void\n}\n\ntype CarouselContextProps = {\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0]\n  api: ReturnType<typeof useEmblaCarousel>[1]\n  scrollPrev: () => void\n  scrollNext: () => void\n  canScrollPrev: boolean\n  canScrollNext: boolean\n} & CarouselProps\n\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null)\n\nfunction useCarousel() {\n  const context = React.useContext(CarouselContext)\n\n  if (!context) {\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\n  }\n\n  return context\n}\n\nfunction Carousel({\n  orientation = \"horizontal\",\n  opts,\n  setApi,\n  plugins,\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & CarouselProps) {\n  const [carouselRef, api] = useEmblaCarousel(\n    {\n      ...opts,\n      axis: orientation === \"horizontal\" ? \"x\" : \"y\",\n    },\n    plugins\n  )\n  const [canScrollPrev, setCanScrollPrev] = React.useState(false)\n  const [canScrollNext, setCanScrollNext] = React.useState(false)\n\n  const onSelect = React.useCallback((api: CarouselApi) => {\n    if (!api) return\n    setCanScrollPrev(api.canScrollPrev())\n    setCanScrollNext(api.canScrollNext())\n  }, [])\n\n  const scrollPrev = React.useCallback(() => {\n    api?.scrollPrev()\n  }, [api])\n\n  const scrollNext = React.useCallback(() => {\n    api?.scrollNext()\n  }, [api])\n\n  const handleKeyDown = React.useCallback(\n    (event: React.KeyboardEvent<HTMLDivElement>) => {\n      if (event.key === \"ArrowLeft\") {\n        event.preventDefault()\n        scrollPrev()\n      } else if (event.key === \"ArrowRight\") {\n        event.preventDefault()\n        scrollNext()\n      }\n    },\n    [scrollPrev, scrollNext]\n  )\n\n  React.useEffect(() => {\n    if (!api || !setApi) return\n    setApi(api)\n  }, [api, setApi])\n\n  React.useEffect(() => {\n    if (!api) return\n    onSelect(api)\n    api.on(\"reInit\", onSelect)\n    api.on(\"select\", onSelect)\n\n    return () => {\n      api?.off(\"select\", onSelect)\n    }\n  }, [api, onSelect])\n\n  return (\n    <CarouselContext.Provider\n      value={{\n        carouselRef,\n        api: api,\n        opts,\n        orientation:\n          orientation || (opts?.axis === \"y\" ? \"vertical\" : \"horizontal\"),\n        scrollPrev,\n        scrollNext,\n        canScrollPrev,\n        canScrollNext,\n      }}\n    >\n      <div\n        onKeyDownCapture={handleKeyDown}\n        className={cn(\"relative\", className)}\n        role=\"region\"\n        aria-roledescription=\"carousel\"\n        data-slot=\"carousel\"\n        {...props}\n      >\n        {children}\n      </div>\n    </CarouselContext.Provider>\n  )\n}\n\nfunction CarouselContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  const { carouselRef, orientation } = useCarousel()\n\n  return (\n    <div\n      ref={carouselRef}\n      className=\"overflow-hidden\"\n      data-slot=\"carousel-content\"\n    >\n      <div\n        className={cn(\n          \"flex\",\n          orientation === \"horizontal\" ? \"-ml-4\" : \"-mt-4 flex-col\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction CarouselItem({ className, ...props }: React.ComponentProps<\"div\">) {\n  const { orientation } = useCarousel()\n\n  return (\n    <div\n      role=\"group\"\n      aria-roledescription=\"slide\"\n      data-slot=\"carousel-item\"\n      className={cn(\n        \"min-w-0 shrink-0 grow-0 basis-full\",\n        orientation === \"horizontal\" ? \"pl-4\" : \"pt-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CarouselPrevious({\n  className,\n  variant = \"outline\",\n  size = \"icon\",\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel()\n\n  return (\n    <Button\n      data-slot=\"carousel-previous\"\n      variant={variant}\n      size={size}\n      className={cn(\n        \"absolute size-8 rounded-full\",\n        orientation === \"horizontal\"\n          ? \"top-1/2 -left-12 -translate-y-1/2\"\n          : \"-top-12 left-1/2 -translate-x-1/2 rotate-90\",\n        className\n      )}\n      disabled={!canScrollPrev}\n      onClick={scrollPrev}\n      {...props}\n    >\n      <ArrowLeft />\n      <span className=\"sr-only\">Previous slide</span>\n    </Button>\n  )\n}\n\nfunction CarouselNext({\n  className,\n  variant = \"outline\",\n  size = \"icon\",\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { orientation, scrollNext, canScrollNext } = useCarousel()\n\n  return (\n    <Button\n      data-slot=\"carousel-next\"\n      variant={variant}\n      size={size}\n      className={cn(\n        \"absolute size-8 rounded-full\",\n        orientation === \"horizontal\"\n          ? \"top-1/2 -right-12 -translate-y-1/2\"\n          : \"-bottom-12 left-1/2 -translate-x-1/2 rotate-90\",\n        className\n      )}\n      disabled={!canScrollNext}\n      onClick={scrollNext}\n      {...props}\n    >\n      <ArrowRight />\n      <span className=\"sr-only\">Next slide</span>\n    </Button>\n  )\n}\n\nexport {\n  type CarouselApi,\n  Carousel,\n  CarouselContent,\n  CarouselItem,\n  CarouselPrevious,\n  CarouselNext,\n}\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AAGA;AAAA;AAEA;AACA;;;AATA;;;;;;AAgCA,MAAM,gCAAkB,CAAA,GAAA,4QAAA,CAAA,gBAAmB,AAAD,EAA+B;AAEzE,SAAS;;IACP,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IAEA,OAAO;AACT;GARS;AAUT,SAAS,SAAS,EAChB,cAAc,YAAY,EAC1B,IAAI,EACJ,MAAM,EACN,OAAO,EACP,SAAS,EACT,QAAQ,EACR,GAAG,OACyC;;IAC5C,MAAM,CAAC,aAAa,IAAI,GAAG,CAAA,GAAA,sRAAA,CAAA,UAAgB,AAAD,EACxC;QACE,GAAG,IAAI;QACP,MAAM,gBAAgB,eAAe,MAAM;IAC7C,GACA;IAEF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EAAE;IACzD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAc,AAAD,EAAE;IAEzD,MAAM,WAAW,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;0CAAE,CAAC;YAClC,IAAI,CAAC,KAAK;YACV,iBAAiB,IAAI,aAAa;YAClC,iBAAiB,IAAI,aAAa;QACpC;yCAAG,EAAE;IAEL,MAAM,aAAa,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;4CAAE;YACnC,KAAK;QACP;2CAAG;QAAC;KAAI;IAER,MAAM,aAAa,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;4CAAE;YACnC,KAAK;QACP;2CAAG;QAAC;KAAI;IAER,MAAM,gBAAgB,CAAA,GAAA,4QAAA,CAAA,cAAiB,AAAD;+CACpC,CAAC;YACC,IAAI,MAAM,GAAG,KAAK,aAAa;gBAC7B,MAAM,cAAc;gBACpB;YACF,OAAO,IAAI,MAAM,GAAG,KAAK,cAAc;gBACrC,MAAM,cAAc;gBACpB;YACF;QACF;8CACA;QAAC;QAAY;KAAW;IAG1B,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;8BAAE;YACd,IAAI,CAAC,OAAO,CAAC,QAAQ;YACrB,OAAO;QACT;6BAAG;QAAC;QAAK;KAAO;IAEhB,CAAA,GAAA,4QAAA,CAAA,YAAe,AAAD;8BAAE;YACd,IAAI,CAAC,KAAK;YACV,SAAS;YACT,IAAI,EAAE,CAAC,UAAU;YACjB,IAAI,EAAE,CAAC,UAAU;YAEjB;sCAAO;oBACL,KAAK,IAAI,UAAU;gBACrB;;QACF;6BAAG;QAAC;QAAK;KAAS;IAElB,qBACE,4SAAC,gBAAgB,QAAQ;QACvB,OAAO;YACL;YACA,KAAK;YACL;YACA,aACE,eAAe,CAAC,MAAM,SAAS,MAAM,aAAa,YAAY;YAChE;YACA;YACA;YACA;QACF;kBAEA,cAAA,4SAAC;YACC,kBAAkB;YAClB,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EAAE,YAAY;YAC1B,MAAK;YACL,wBAAqB;YACrB,aAAU;YACT,GAAG,KAAK;sBAER;;;;;;;;;;;AAIT;IAxFS;;QASoB,sRAAA,CAAA,UAAgB;;;KATpC;AA0FT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;;IAC3E,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAErC,qBACE,4SAAC;QACC,KAAK;QACL,WAAU;QACV,aAAU;kBAEV,cAAA,4SAAC;YACC,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,QACA,gBAAgB,eAAe,UAAU,kBACzC;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;IAnBS;;QAC8B;;;MAD9B;AAqBT,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;;IACxE,MAAM,EAAE,WAAW,EAAE,GAAG;IAExB,qBACE,4SAAC;QACC,MAAK;QACL,wBAAqB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,sCACA,gBAAgB,eAAe,SAAS,QACxC;QAED,GAAG,KAAK;;;;;;AAGf;IAhBS;;QACiB;;;MADjB;AAkBT,SAAS,iBAAiB,EACxB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC;;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,4SAAC,8JAAA,CAAA,SAAM;QACL,aAAU;QACV,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gCACA,gBAAgB,eACZ,sCACA,+CACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,4SAAC,uSAAA,CAAA,YAAS;;;;;0BACV,4SAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IA5BS;;QAM4C;;;MAN5C;AA8BT,SAAS,aAAa,EACpB,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,MAAM,EACb,GAAG,OACiC;;IACpC,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;IAEnD,qBACE,4SAAC,8JAAA,CAAA,SAAM;QACL,aAAU;QACV,SAAS;QACT,MAAM;QACN,WAAW,CAAA,GAAA,+IAAA,CAAA,KAAE,AAAD,EACV,gCACA,gBAAgB,eACZ,uCACA,kDACJ;QAEF,UAAU,CAAC;QACX,SAAS;QACR,GAAG,KAAK;;0BAET,4SAAC,ySAAA,CAAA,aAAU;;;;;0BACX,4SAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;AAGhC;IA5BS;;QAM4C;;;MAN5C", "debugId": null}}, {"offset": {"line": 1414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/testimonials.tsx"], "sourcesContent": ["'use client';\n\nimport {\n  Avatar,\n  AvatarFallback,\n  AvatarImage,\n} from '@repo/design-system/components/ui/avatar';\nimport {\n  Carousel,\n  type CarouselApi,\n  CarouselContent,\n  CarouselItem,\n} from '@repo/design-system/components/ui/carousel';\nimport type { Dictionary } from '@repo/internationalization';\nimport { User } from 'lucide-react';\nimport { useEffect, useState } from 'react';\n\ntype TestimonialsProps = {\n  dictionary: Dictionary;\n};\n\nexport const Testimonials = ({ dictionary }: TestimonialsProps) => {\n  const [api, setApi] = useState<CarouselApi>();\n  const [current, setCurrent] = useState(0);\n\n  useEffect(() => {\n    if (!api) {\n      return;\n    }\n\n    setTimeout(() => {\n      if (api.selectedScrollSnap() + 1 === api.scrollSnapList().length) {\n        setCurrent(0);\n        api.scrollTo(0);\n      } else {\n        api.scrollNext();\n        setCurrent(current + 1);\n      }\n    }, 4000);\n  }, [api, current]);\n\n  return (\n    <div className=\"w-full py-20 lg:py-40\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex flex-col gap-10\">\n          <h2 className=\"text-left font-regular text-3xl tracking-tighter md:text-5xl lg:max-w-xl\">\n            {dictionary.web.home.testimonials.title}\n          </h2>\n          <Carousel setApi={setApi} className=\"w-full\">\n            <CarouselContent>\n              {dictionary.web.home.testimonials.items.map((item, index) => (\n                <CarouselItem className=\"lg:basis-1/2\" key={index}>\n                  <div className=\"flex aspect-video h-full flex-col justify-between rounded-md bg-muted p-6 lg:col-span-2\">\n                    <User className=\"h-8 w-8 stroke-1\" />\n                    <div className=\"flex flex-col gap-4\">\n                      <div className=\"flex flex-col\">\n                        <h3 className=\"text-xl tracking-tight\">{item.title}</h3>\n                        <p className=\"max-w-xs text-base text-muted-foreground\">\n                          {item.description}\n                        </p>\n                      </div>\n                      <p className=\"flex flex-row items-center gap-2 text-sm\">\n                        <span className=\"text-muted-foreground\">By</span>\n                        <Avatar className=\"h-6 w-6\">\n                          <AvatarImage src={item.author.image} />\n                          <AvatarFallback>??</AvatarFallback>\n                        </Avatar>\n                        <span>{item.author.name}</span>\n                      </p>\n                    </div>\n                  </div>\n                </CarouselItem>\n              ))}\n            </CarouselContent>\n          </Carousel>\n        </div>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AAKA;AAOA;AACA;;;AAfA;;;;;AAqBO,MAAM,eAAe,CAAC,EAAE,UAAU,EAAqB;;IAC5D,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD;IAC7B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;kCAAE;YACR,IAAI,CAAC,KAAK;gBACR;YACF;YAEA;0CAAW;oBACT,IAAI,IAAI,kBAAkB,KAAK,MAAM,IAAI,cAAc,GAAG,MAAM,EAAE;wBAChE,WAAW;wBACX,IAAI,QAAQ,CAAC;oBACf,OAAO;wBACL,IAAI,UAAU;wBACd,WAAW,UAAU;oBACvB;gBACF;yCAAG;QACL;iCAAG;QAAC;QAAK;KAAQ;IAEjB,qBACE,4SAAC;QAAI,WAAU;kBACb,cAAA,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAG,WAAU;kCACX,WAAW,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK;;;;;;kCAEzC,4SAAC,gKAAA,CAAA,WAAQ;wBAAC,QAAQ;wBAAQ,WAAU;kCAClC,cAAA,4SAAC,gKAAA,CAAA,kBAAe;sCACb,WAAW,GAAG,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBACjD,4SAAC,gKAAA,CAAA,eAAY;oCAAC,WAAU;8CACtB,cAAA,4SAAC;wCAAI,WAAU;;0DACb,4SAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,4SAAC;gDAAI,WAAU;;kEACb,4SAAC;wDAAI,WAAU;;0EACb,4SAAC;gEAAG,WAAU;0EAA0B,KAAK,KAAK;;;;;;0EAClD,4SAAC;gEAAE,WAAU;0EACV,KAAK,WAAW;;;;;;;;;;;;kEAGrB,4SAAC;wDAAE,WAAU;;0EACX,4SAAC;gEAAK,WAAU;0EAAwB;;;;;;0EACxC,4SAAC,8JAAA,CAAA,SAAM;gEAAC,WAAU;;kFAChB,4SAAC,8JAAA,CAAA,cAAW;wEAAC,KAAK,KAAK,MAAM,CAAC,KAAK;;;;;;kFACnC,4SAAC,8JAAA,CAAA,iBAAc;kFAAC;;;;;;;;;;;;0EAElB,4SAAC;0EAAM,KAAK,MAAM,CAAC,IAAI;;;;;;;;;;;;;;;;;;;;;;;;mCAhBa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4B5D;GA1Da;KAAA", "debugId": null}}, {"offset": {"line": 1617, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/%28home%29/components/trusted-by.tsx"], "sourcesContent": ["'use client';\n\ntype TrustedByProps = {\n  dictionary: any;\n};\n\nexport const TrustedBy = ({ dictionary }: TrustedByProps) => (\n  <div className=\"w-full relative overflow-hidden py-20 lg:py-32\">\n    {/* Grid background pattern */}\n    <div\n      className=\"absolute inset-0 opacity-20\"\n      style={{\n        backgroundImage: `\n          linear-gradient(rgba(255,255,255,0.1) 1px, transparent 1px),\n          linear-gradient(90deg, rgba(255,255,255,0.1) 1px, transparent 1px)\n        `,\n        backgroundSize: '40px 40px'\n      }}\n    />\n\n    {/* Grid lines with more transparency */}\n    <div className=\"absolute inset-0 pointer-events-none z-10\">\n      {/* Left vertical dashed line */}\n      <div\n        className=\"absolute top-0 bottom-0 w-px\"\n        style={{\n          left: '10%',\n          background: 'repeating-linear-gradient(to bottom, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\n        }}\n      />\n\n      {/* Right vertical dashed line */}\n      <div\n        className=\"absolute top-0 bottom-0 w-px\"\n        style={{\n          right: '10%',\n          background: 'repeating-linear-gradient(to bottom, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\n        }}\n      />\n\n      {/* Top horizontal dashed line */}\n      <div\n        className=\"absolute left-0 right-0 h-px\"\n        style={{\n          top: '92px',\n          background: 'repeating-linear-gradient(to right, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\n        }}\n      />\n\n      {/* Bottom horizontal dashed line */}\n      <div\n        className=\"absolute left-0 right-0 h-px\"\n        style={{\n          bottom: '92px',\n          background: 'repeating-linear-gradient(to right, rgba(62, 62, 62, 0.3) 0 5px, transparent 5px 11px)'\n        }}\n      />\n    </div>\n\n    <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 relative z-20\">\n      <div className=\"flex flex-col items-center justify-center gap-12\">\n        <div className=\"flex flex-col gap-4 text-center\">\n          <h2 className=\"font-regular text-3xl tracking-tighter md:text-4xl\">\n            Trusted by developers worldwide\n          </h2>\n          <p className=\"max-w-2xl text-lg text-muted-foreground leading-relaxed tracking-tight\">\n            Join thousands of developers who rely on Cubent to enhance their coding workflow\n          </p>\n        </div>\n\n        {/* Company logos grid */}\n        <div className=\"w-full max-w-6xl\">\n          <div className=\"grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center justify-items-center opacity-60 hover:opacity-80 transition-opacity\">\n            {/* Notion Logo */}\n            <div className=\"flex flex-col items-center gap-2\">\n              <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M4.459 4.208c.746.606 1.026.56 2.428.466l13.215-.793c.28 0 .047-.28-.046-.326L17.86 1.968c-.42-.326-.981-.7-2.055-.607L3.01 2.295c-.466.046-.56.28-.374.466zm.793 3.08v13.904c0 .747.373 1.027 1.214.98l14.523-.84c.841-.046.935-.56.935-1.167V6.354c0-.606-.233-.933-.748-.887l-15.177.887c-.56.047-.747.327-.747.933zm14.337.745c.093.42 0 .84-.42.888l-.7.14v10.264c-.608.327-1.168.514-1.635.514-.748 0-.935-.234-1.495-.933l-4.577-7.186v6.952L12.21 19s0 .84-1.168.84l-3.222.186c-.093-.186 0-.653.327-.746l.84-.233V9.854L7.822 9.76c-.094-.42.14-1.026.793-1.073l3.456-.233 4.764 7.279v-6.44l-1.215-.139c-.093-.514.28-.887.747-.933zM1.936 1.035l13.31-.98c1.634-.14 2.055-.047 3.082.7l4.249 2.986c.7.513.934.653.934 1.213v16.378c0 1.026-.373 1.634-1.68 1.726l-15.458.934c-.98.047-1.448-.093-1.962-.747l-3.129-4.06c-.56-.747-.793-1.306-.793-1.96V2.667c0-.839.374-1.54 1.447-1.632z\"/>\n              </svg>\n              <span className=\"text-xs text-muted-foreground font-medium\">Notion</span>\n            </div>\n\n            {/* Figma Logo */}\n            <div className=\"flex flex-col items-center gap-2\">\n              <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M15.852 8.981h-4.588V0h4.588c2.476 0 4.49 2.014 4.49 4.49s-2.014 4.491-4.49 4.491zM12.735 7.51h3.117c1.665 0 3.019-1.355 3.019-3.019s-1.354-3.019-3.019-3.019h-3.117V7.51zm0 1.471H8.148c-2.476 0-4.49-2.015-4.49-4.491S5.672 0 8.148 0h4.588v8.981zm-4.587-7.51c-1.665 0-3.019 1.355-3.019 3.019s1.354 3.02 3.019 3.02h3.117V1.471H8.148zm4.587 15.019H8.148c-2.476 0-4.49-2.014-4.49-4.49s2.014-4.49 4.49-4.49h4.588v8.98zM8.148 8.981c-1.665 0-3.019 1.355-3.019 3.019s1.355 3.019 3.019 3.019h3.117V8.981H8.148zM8.172 24c-2.489 0-4.515-2.014-4.515-4.49s2.014-4.49 4.49-4.49h4.588v4.441c0 2.503-2.047 4.539-4.563 4.539zm-.024-7.51a3.023 3.023 0 0 0-3.019 3.019c0 1.665 1.365 3.019 3.044 3.019 1.705 0 3.093-1.376 3.093-3.068v-2.97H8.148z\"/>\n                <path d=\"M12.764 24c-2.516 0-4.563-2.036-4.563-4.539s2.047-4.539 4.563-4.539 4.564 2.036 4.564 4.539S15.28 24 12.764 24zm0-7.588a3.023 3.023 0 0 0-3.093 3.049c0 1.691 1.387 3.068 3.093 3.068s3.093-1.377 3.093-3.068-1.387-3.049-3.093-3.049z\"/>\n              </svg>\n              <span className=\"text-xs text-muted-foreground font-medium\">Figma</span>\n            </div>\n\n            {/* Discord Logo */}\n            <div className=\"flex flex-col items-center gap-2\">\n              <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419-.0002 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9554 2.4189-2.1568 2.4189Z\"/>\n              </svg>\n              <span className=\"text-xs text-muted-foreground font-medium\">Discord</span>\n            </div>\n\n            {/* Slack Logo */}\n            <div className=\"flex flex-col items-center gap-2\">\n              <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313zM8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.528 2.528 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.528 2.528 0 0 1 2.521 2.521 2.528 2.528 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312zM18.956 8.834a2.528 2.528 0 0 1 2.522-2.521A2.528 2.528 0 0 1 24 8.834a2.528 2.528 0 0 1-2.522 2.521h-2.522V8.834zM17.688 8.834a2.528 2.528 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.527 2.527 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312zM15.165 18.956a2.528 2.528 0 0 1 2.523 2.522A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.522v-2.522h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.526 2.526 0 0 1 2.52-2.52h6.313A2.527 2.527 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z\"/>\n              </svg>\n              <span className=\"text-xs text-muted-foreground font-medium\">Slack</span>\n            </div>\n\n            {/* Spotify Logo */}\n            <div className=\"flex flex-col items-center gap-2\">\n              <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M12 0C5.4 0 0 5.4 0 12s5.4 12 12 12 12-5.4 12-12S18.66 0 12 0zm5.521 17.34c-.24.359-.66.48-1.021.24-2.82-1.74-6.36-2.101-10.561-1.141-.418.122-.779-.179-.899-.539-.12-.421.18-.78.54-.9 4.56-1.021 8.52-.6 11.64 1.32.42.18.479.659.301 1.02zm1.44-3.3c-.301.42-.841.6-1.262.3-3.239-1.98-8.159-2.58-11.939-1.38-.479.12-1.02-.12-1.14-.6-.12-.48.12-1.021.6-1.141C9.6 9.9 15 10.561 18.72 12.84c.361.181.54.78.241 1.2zm.12-3.36C15.24 8.4 8.82 8.16 5.16 9.301c-.6.179-1.2-.181-1.38-.721-.18-.601.18-1.2.72-1.381 4.26-1.26 11.28-1.02 15.721 1.621.539.3.719 1.02.42 1.56-.299.421-1.02.599-1.559.3z\"/>\n              </svg>\n              <span className=\"text-xs text-muted-foreground font-medium\">Spotify</span>\n            </div>\n\n            {/* Dropbox Logo */}\n            <div className=\"flex flex-col items-center gap-2\">\n              <svg className=\"h-8 w-8 text-muted-foreground\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                <path d=\"M6 2L12 6.5 6 11 0 6.5 6 2zm6 4.5L18 2l6 4.5L18 11l-6-4.5zM0 13.5L6 9l6 4.5L6 18l-6-4.5zm18-4.5l6 4.5L18 18l-6-4.5L18 9zM6 20.5l6-4.5 6 4.5L12 25l-6-4.5z\"/>\n              </svg>\n              <span className=\"text-xs text-muted-foreground font-medium\">Dropbox</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Stats section */}\n        <div className=\"w-full max-w-2xl -mt-8\">\n          <div className=\"grid grid-cols-3 gap-0 text-center rounded-lg overflow-hidden\">\n            <div className=\"flex flex-col gap-1 py-4 px-3\">\n              <div className=\"text-2xl font-bold tracking-tight\" style={{ color: '#888888' }}>2.5k</div>\n              <div className=\"text-xs\" style={{ color: '#999999' }}>developers</div>\n            </div>\n            <div className=\"flex flex-col gap-1 py-4 px-3\">\n              <div className=\"text-2xl font-bold tracking-tight\" style={{ color: '#888888' }}>2.5M+</div>\n              <div className=\"text-xs\" style={{ color: '#999999' }}>Lines of Code</div>\n            </div>\n            <div className=\"flex flex-col gap-1 py-4 px-3\">\n              <div className=\"text-2xl font-bold tracking-tight\" style={{ color: '#888888' }}>99.9%</div>\n              <div className=\"text-xs\" style={{ color: '#999999' }}>Uptime</div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  </div>\n);\n"], "names": [], "mappings": ";;;;AAAA;;AAMO,MAAM,YAAY,CAAC,EAAE,UAAU,EAAkB,iBACtD,4SAAC;QAAI,WAAU;;0BAEb,4SAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,CAAC;;;QAGlB,CAAC;oBACD,gBAAgB;gBAClB;;;;;;0BAIF,4SAAC;gBAAI,WAAU;;kCAEb,4SAAC;wBACC,WAAU;wBACV,OAAO;4BACL,MAAM;4BACN,YAAY;wBACd;;;;;;kCAIF,4SAAC;wBACC,WAAU;wBACV,OAAO;4BACL,OAAO;4BACP,YAAY;wBACd;;;;;;kCAIF,4SAAC;wBACC,WAAU;wBACV,OAAO;4BACL,KAAK;4BACL,YAAY;wBACd;;;;;;kCAIF,4SAAC;wBACC,WAAU;wBACV,OAAO;4BACL,QAAQ;4BACR,YAAY;wBACd;;;;;;;;;;;;0BAIJ,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,4SAAC;oCAAE,WAAU;8CAAyE;;;;;;;;;;;;sCAMxF,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;;kDAEb,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;gDAAgC,SAAQ;gDAAY,MAAK;0DACtE,cAAA,4SAAC;oDAAK,GAAE;;;;;;;;;;;0DAEV,4SAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;;kDAI9D,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;gDAAgC,SAAQ;gDAAY,MAAK;;kEACtE,4SAAC;wDAAK,GAAE;;;;;;kEACR,4SAAC;wDAAK,GAAE;;;;;;;;;;;;0DAEV,4SAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;;kDAI9D,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;gDAAgC,SAAQ;gDAAY,MAAK;0DACtE,cAAA,4SAAC;oDAAK,GAAE;;;;;;;;;;;0DAEV,4SAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;;kDAI9D,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;gDAAgC,SAAQ;gDAAY,MAAK;0DACtE,cAAA,4SAAC;oDAAK,GAAE;;;;;;;;;;;0DAEV,4SAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;;kDAI9D,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;gDAAgC,SAAQ;gDAAY,MAAK;0DACtE,cAAA,4SAAC;oDAAK,GAAE;;;;;;;;;;;0DAEV,4SAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;;kDAI9D,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;gDAAgC,SAAQ;gDAAY,MAAK;0DACtE,cAAA,4SAAC;oDAAK,GAAE;;;;;;;;;;;0DAEV,4SAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;;;;;;;;;;;;;sCAMlE,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;gDAAoC,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;0DAChF,4SAAC;gDAAI,WAAU;gDAAU,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;;kDAExD,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;gDAAoC,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;0DAChF,4SAAC;gDAAI,WAAU;gDAAU,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;;kDAExD,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;gDAAoC,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;0DAChF,4SAAC;gDAAI,WAAU;gDAAU,OAAO;oDAAE,OAAO;gDAAU;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KAnIvD", "debugId": null}}]}